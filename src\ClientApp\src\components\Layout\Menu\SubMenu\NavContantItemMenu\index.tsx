import { useNavigate } from 'react-router-dom';
import { Box } from '@chakra-ui/react';
import { RxBackpack, RxUpdate } from 'react-icons/rx';
import { FiPlayCircle } from 'react-icons/fi';
import { GiHandTruck } from 'react-icons/gi';
import { BiError } from 'react-icons/bi';
import { AiOutlineAudit, AiTwotoneFire } from 'react-icons/ai';
import { FaRegAddressCard, FaFileInvoiceDollar } from 'react-icons/fa';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { CadastroRevendas, FiscalIcon } from 'icons';

import { ContentItemMenu } from '../ContentItemMenu';

type NavContantItemMenu = {
  hasDisplayIcon: boolean;
};

export const NavContantItemMenu = ({ hasDisplayIcon }: NavContantItemMenu) => {
  const navigate = useNavigate();

  const handleNavigateTo = (rota: string) => {
    navigate(rota);
  };

  const openHangfire = () => {
    const url = import.meta.env.VITE_API_URL
      ? import.meta.env.VITE_API_URL.replace('/api', '/hangfire')
      : '';
    window.open(url, '_blank')?.focus();
  };

  return (
    <Box>
      <ContentItemMenu
        iconMenu={FaRegAddressCard}
        keyMenu="assinaturas"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.ASSINATURAS);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_ASSINATURA
        )}
      >
        Assinaturas
      </ContentItemMenu>
      <ContentItemMenu
        iconMenu={FaFileInvoiceDollar}
        keyMenu="faturamento-exibicao"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.FATURAMENTO_EXIBICAO);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_EXIBIR
        )}
      >
        Faturamento
      </ContentItemMenu>

      <ContentItemMenu
        iconMenu={RxBackpack}
        keyMenu="revenda"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.REVENDA);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_REVENDA
        )}
      >
        Revendas
      </ContentItemMenu>

      <ContentItemMenu
        iconMenu={AiOutlineAudit}
        keyMenu="conferencia"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.FATURAMENTO_CONFERENCIA);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_FATURAMENTO_CONFERENCIA
        )}
      >
        Conferência
      </ContentItemMenu>

      <ContentItemMenu
        hasItemContent
        keyMenu="cadastros"
        hasDisplayIcon={hasDisplayIcon}
        iconMenu={CadastroRevendas}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO
        )}
        valueItemContent={[
          {
            title: 'Produtos',
            keyMenu: 'cadastros',
            colorHover: 'gray.100',
            onClick: () => {
              navigate(ConstanteRotas.PRODUTOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_PRODUTO
            ),
          },
          {
            title: 'Serviços',
            keyMenu: 'servicos',
            colorHover: 'gray.100',
            onClick: () => {
              navigate(ConstanteRotas.SERVICOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_SERVICO
            ),
          },
          {
            title: 'Grade de serviços',
            keyMenu: 'cadastros',
            colorHover: 'gray.100',
            onClick: () => {
              navigate(ConstanteRotas.GRADE_SERVICOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_GRADE_SERVICO
            ),
          },
          {
            title: 'Usuários',
            keyMenu: 'cadastros',
            colorHover: 'gray.100',
            onClick: () => {
              navigate(ConstanteRotas.USUARIOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_USUARIO
            ),
          },
        ]}
      >
        Cadastros
      </ContentItemMenu>
      <ContentItemMenu
        iconMenu={RxUpdate}
        keyMenu="atualizacao"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.ATUALIZACAO);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_ATUALIZACAO
        )}
      >
        Atualizações
      </ContentItemMenu>
      <ContentItemMenu
        hasItemContent
        hasDisplayIcon={hasDisplayIcon}
        iconMenu={FiscalIcon}
        keyMenu="fiscal"
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_FISCAL
        )}
        valueItemContent={[
          {
            title: 'Regras fiscais Uf',
            colorHover: 'gray.100',
            keyMenu: 'fiscal',
            onClick: () => {
              navigate(ConstanteRotas.REGRAS_FISCAL_UF);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_REGRA_FISCAL
            ),
          },
          {
            title: 'Nota fiscal serv. Urls',
            colorHover: 'gray.100',
            keyMenu: 'fiscal',
            onClick: () => {
              navigate(ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NOTA_FISCAL_URL
            ),
          },
          {
            title: 'Nfc-e QrCode',
            colorHover: 'gray.100',
            keyMenu: 'fiscal',
            onClick: () => {
              navigate(ConstanteRotas.NFCE_QRCODE);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NFCE
            ),
          },
          {
            title: 'Nota fiscal rejeições',
            colorHover: 'gray.100',
            keyMenu: 'fiscal',
            onClick: () => {
              navigate(ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NF_REJEICAO
            ),
          },
          {
            title: 'Nota fiscal validações',
            colorHover: 'gray.100',
            keyMenu: 'fiscal',
            onClick: () => {
              navigate(ConstanteRotas.NOTA_FISCAL_VALIDACOES);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NF_VALIDACAO
            ),
          },
        ]}
      >
        Fiscal
      </ContentItemMenu>
      <ContentItemMenu
        hasItemContent
        keyMenu="importacao"
        hasDisplayIcon={hasDisplayIcon}
        iconMenu={GiHandTruck}
        valueItemContent={[
          {
            title: 'NCM',
            keyMenu: 'importacao',
            colorHover: 'gray.100',
            onClick: () => {
              navigate(ConstanteRotas.IMPORTACAO_NCM_LISTAR);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_IMPORTACAO
            ),
          },
        ]}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.ImportacaoNcmAcao.VISUALIZAR_IMPORTACAO_NCM
        )}
      >
        Importações
      </ContentItemMenu>
      <ContentItemMenu
        iconMenu={BiError}
        keyMenu="logErros"
        hasDisplayIcon={hasDisplayIcon}
        handlePushNavigation={() => {
          navigate(ConstanteRotas.LOG_ERROS);
        }}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_LOG
        )}
      >
        Log de erros
      </ContentItemMenu>
      <ContentItemMenu
        hasDisplayIcon={hasDisplayIcon}
        iconMenu={AiTwotoneFire}
        keyMenu="hangfire"
        handlePushNavigation={() => openHangfire()}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_HANG_FIRE
        )}
      >
        Hangfire
      </ContentItemMenu>
      <ContentItemMenu
        hasItemContent
        iconMenu={FiPlayCircle}
        hasDisplayIcon={hasDisplayIcon}
        keyMenu="zenflix"
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AcessoMenu.MENU_TREINAMENTOS
        )}
        valueItemContent={[
          {
            title: 'Banners',
            colorHover: 'gray.100',
            keyMenu: 'zenflix',
            onClick: () => {
              handleNavigateTo(ConstanteRotas.BANNERS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_BANNER
            ),
          },
          {
            title: 'Categorias',
            colorHover: 'gray.100',
            keyMenu: 'zenflix',
            onClick: () => {
              handleNavigateTo(ConstanteRotas.CATEGORIAS_TREINAMENTO);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_CATEGORIAS_TREINAMENTO
            ),
          },
          {
            title: 'Temas',
            colorHover: 'gray.100',
            keyMenu: 'zenflix',
            onClick: () => {
              handleNavigateTo(ConstanteRotas.TEMAS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_TEMAS
            ),
          },
          {
            isSeparator: true,
            keyMenu: 'zenflix',
          },
          {
            title: 'Artigos',
            colorHover: 'gray.100',
            keyMenu: 'zenflix',
            onClick: () => {
              handleNavigateTo(ConstanteRotas.ARTIGOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_ARTIGOS_VIDEOS
            ),
          },
          {
            title: 'Videos',
            colorHover: 'gray.100',
            keyMenu: 'zenflix',
            onClick: () => {
              handleNavigateTo(ConstanteRotas.VIDEOS);
            },
            possuiFuncionalidade: auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AcessoMenu.MENU_ARTIGOS_VIDEOS
            ),
          },
        ]}
      >
        Artigos e Vídeos
      </ContentItemMenu>
    </Box>
  );
};
