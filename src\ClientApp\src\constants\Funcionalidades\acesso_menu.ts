import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';

export const AcessoMenu = {
  MENU_ASSINATURA: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.SISTEMA_FINANCEIRO,

    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.SUPORTE_STI3,

    EnumTipoUsuario.REVENDA_ADMIN,
    EnumTipoUsuario.REVENDA_ASSISTENTE,

    EnumTipoUsuario.DESENVOLVEDOR,
  ],

MENU_FATURAMENTO_EXIBICAO: [
  EnumTipoUsuario.SISTEMA_ADMIN,
  EnumTipoUsuario.SISTEMA_FINANCEIRO,
  
  EnumTipoUsuario.CANAIS_GERENTE,
  EnumTipoUsuario.SUPORTE_STI3,
  
  EnumTipoUsuario.REVENDA_ADMIN,
  
  EnumTipoUsuario.DESENVOLVEDOR,
],

MENU_FATURAMENTO_CONFERENCIA: [
  EnumTipoUsuario.SISTEMA_ADMIN,
  EnumTipoUsuario.SISTEMA_FINANCEIRO,  
  EnumTipoUsuario.CANAIS_GERENTE,  
  EnumTipoUsuario.DESENVOLVEDOR,
],

  MENU_REVENDA: [
    EnumTipoUsuario.SISTEMA_ADMIN,

    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.SUPORTE_STI3,

    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  //#region CADASTRO
  MENU_CADASTRO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_CADASTRO_PRODUTO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_CADASTRO_SERVICO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_CADASTRO_GRADE_SERVICO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
    EnumTipoUsuario.CANAIS_GERENTE,
  ],

  MENU_CADASTRO_USUARIO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  //#endregion

  MENU_ATUALIZACAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  //#region FISCAL
  MENU_FISCAL: [
    EnumTipoUsuario.SISTEMA_ADMIN, 
    EnumTipoUsuario.DESENVOLVEDOR
  ],

  MENU_FISCAL_REGRA_FISCAL: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_FISCAL_NOTA_FISCAL_URL: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_FISCAL_NFCE: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_FISCAL_NF_REJEICAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_FISCAL_NF_VALIDACAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  //#endregion

  MENU_IMPORTACAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_LOG: [EnumTipoUsuario.SISTEMA_ADMIN, EnumTipoUsuario.DESENVOLVEDOR],

  MENU_HANG_FIRE: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  //#region TREINAMENTOS
  MENU_TREINAMENTOS: [
    EnumTipoUsuario.ANALISTA_CONTEUDO,
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],

  MENU_ARTIGOS_VIDEOS: [
    EnumTipoUsuario.ANALISTA_CONTEUDO,
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  MENU_TEMAS: [
    EnumTipoUsuario.ANALISTA_CONTEUDO,
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  MENU_CATEGORIAS_TREINAMENTO: [
    EnumTipoUsuario.ANALISTA_CONTEUDO,
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  MENU_BANNER: [
    EnumTipoUsuario.ANALISTA_CONTEUDO,
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  //#endregion
};
