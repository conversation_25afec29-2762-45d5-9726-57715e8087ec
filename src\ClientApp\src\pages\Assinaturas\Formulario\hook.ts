import auth from 'modules/auth';

import {
  DominiosDeliveryEnum,
  optionsFomerZendar,
  optionsPowerStockChef,
} from 'constants/Enum/enumDominios';

export const useAssinaturaFormulario = () => {
  const usuarioNomeDominio = auth.getUserNameDominio();
  const usuarioRevendaPrincipal = usuarioNomeDominio.includes('zendar');

  const opcoesDominiosDelivery = Object.entries(DominiosDeliveryEnum).map(
    ([_, value]) => ({ label: value, value })
  );

  const getDominioDefault = () => {
    const optionsDominios = usuarioRevendaPrincipal
      ? optionsFomerZendar
      : optionsPowerStockChef;

    const defaultDominio =
      optionsDominios.find((item) => item.value === usuarioNomeDominio) ??
      optionsDominios[0];
    return defaultDominio.value;
  };

  const opcaoPadraoDominioDelivery = opcoesDominiosDelivery.find((opcao) => {
    if (usuarioRevendaPrincipal) {
      return opcao.value === DominiosDeliveryEnum.DOMINIO_STI3;
    }

    return opcao.value === DominiosDeliveryEnum.DOMINIO_REVENDA;
  });

  // Calculate default expiration date (20th of next month in the following year)
  const getDefaultExpirationDate = () => {
    const today = new Date();
    const nextYear = today.getFullYear() + 1;
    const nextMonth = today.getMonth() + 1;
    return new Date(nextYear, nextMonth, 20); // Month is 0-indexed, so nextMonth gives us the month after current
  };

  const formDefaultValues = {
    nome: '',
    cidadeNome: null,
    faturamento: null,
    telefoneResponsavel: '',
    email: '',
    dominio: '',
    urlDominio: getDominioDefault(),
    dominioDelivery: '',
    urlDominioDelivery: opcaoPadraoDominioDelivery?.value,
    usuario: 'admin',
    cnpj: '',
    razaoSocial: '',
    fantasia: '',
    inscricaoEstadual: '',
    inscricaoMunicipal: '',
    codigoExterno: '',
    observacao: '',
    cep: '',
    logradouro: '',
    numero: '',
    bairro: '',
    celular: '',
    telefone: '',
    emailContato: '',
    tabelaPreco: null,
    paisNome: { value: 1, label: 'Brasil' },
    complemento: '',
    idAssinaturaBasePrecos: null,
    codigoERP: '',
    diaVencimento: '10',
    dataExpiracao: getDefaultExpirationDate(),
    ultimaValidacao: getDefaultExpirationDate(),
    validacaoAutomatica: true,
  };

  return {
    formDefaultValues,
    opcoesDominiosDelivery,
    opcaoPadraoDominioDelivery,
    usuarioRevendaPrincipal,
    usuarioNomeDominio,
  };
};
