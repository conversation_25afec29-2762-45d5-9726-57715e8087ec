import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Flex, Link } from '@chakra-ui/react';
import { useSortable } from '@dnd-kit/sortable';

import auth from 'modules/auth';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import {
  SubstituirParametroRota,
  ConstanteRotas,
} from 'constants/constantRotas';

import { ActionsMenu } from 'components/ActionsMenu';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';

import { ServicosProps } from '../validationForms';

export interface ItemServicoDraggableProp {
  servico: ServicosProps;
  recarregarListagem?: boolean;
  handleExcluirServico: (id: string) => void;
}

export const ItemServicoDraggable = ({
  servico,
  handleExcluirServico,
}: ItemServicoDraggableProp) => {
  const navigate = useNavigate();

  const possuiPermissaoAlterarServico = auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroServicoAcao.ALTERAR_SERVICOS);

  const { attributes, isDragging, listeners, setNodeRef } = useSortable({
    id: servico.id,
  });

  const handleAlterarServico = useCallback(
    (servicoId: string) => {
      if (!possuiPermissaoAlterarServico) return;
      navigate(
        SubstituirParametroRota(
          ConstanteRotas.SERVICOS_ALTERAR,
          'id',
          servicoId
        )
      );
    },
    [possuiPermissaoAlterarServico]
  );

  return (
    <Flex
      w="100%"
      opacity={isDragging ? 0.4 : undefined}
      bg={isDragging ? 'transparent' : 'gray.50'}
      p="12px"
      mt="8px"
      border={isDragging ? '1px dashed teal' : '1px solid lightgray'}
      borderRadius="4px"
      alignItems="center"
      justifyContent="space-between"
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      <Flex gap="16px" w="full">
        <StatusCircle isActive={servico.ativo} />
        <Link
          lineHeight="12.5px"
          whiteSpace="pre-line"
          cursor="pointer"
          userSelect="none"
          onClick={() => handleAlterarServico(servico.id)}
        >
          {servico.nome}
        </Link>
      </Flex>

      <Flex alignSelf="flex-end">
        <ActionsMenu
          id="servicoAcoes"
          menuZIndex="base"
          items={[
            {
              content: 'Editar',
              onClick: () => {
                handleAlterarServico(servico.id);
              },
              possuiFuncionalidade: auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroServicoAcao.ALTERAR_SERVICOS),
            },
            {
              content: 'Excluir',
              onClick: () => {
                handleExcluirServico(servico.id);
              },
              possuiFuncionalidade: auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroServicoAcao.EXCLUIR_SERVICOS),
            },
          ]}
        />
      </Flex>
    </Flex>
  );
};
