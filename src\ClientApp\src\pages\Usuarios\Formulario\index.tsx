import { GridItem } from '@chakra-ui/react';

import { CheckboxAtivoInativo } from 'components/Checkbox/CheckboxAtivoInativo';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';
import auth from 'modules/auth';

export const FormUsuario = ({ isSistemaAdmin = false }) => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          name="nome"
          placeholder="Informe o nome"
          label="Nome"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          name="login"
          placeholder="Informe seu login de acesso ao sistema"
          blockSpecialCharacters
          label="Login"
          isRequired
        />
      </GridItem>

      <GridItem colSpan={[12, 4, 4]} mb="1">
        <CheckboxAtivoInativo
          name="ativo"
          label="Status"
          fontLabel="xs"
          colorLabel="black"
        />
      </GridItem>

      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          label="E-mail"
          placeholder="Informe o e-mail"
          isRequired
          name="email"
        />
      </GridItem>
      {!auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR) ? (
        <>
          <GridItem colSpan={[12, 12, 6]}>
            <SelectDefault
              label="Perfil"
              isRequired
              name="tipoUsuario"
              asControlledByObject={false}
              options={EnumTipoUsuario.cadastroUsuario}
            />
          </GridItem>
        </>
      ) : null}
    </SimpleGridForm>
  );
};
