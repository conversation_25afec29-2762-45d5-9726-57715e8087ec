import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';
import { useAssinaturasContext } from 'store/Assinaturas/AssinaturaContext';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormAssinaturas } from '..';
import { dadosApiAssinatura } from '../dataApi';
import {
  FormData,
  yupResolverCadastroLoja,
  yupResolverCadastroCompleto,
} from '../validationForms';
import { useAssinaturaFormulario } from '../hook';
  const getDefaultExpirationDate = () => {
    const today = new Date();
    const nextYear = today.getFullYear() + 1;
    const nextMonth = today.getMonth() + 1;
    return new Date(nextYear, nextMonth, 20); // Month is 0-indexed, so nextMonth gives us the month after current
  };
export const CadastrarAssinaturas = () => {
  const [isLoading, setIsLoading] = useState(false);

  const { formDefaultValues } = useAssinaturaFormulario();

  const {
    isCriarNovaLoja,
    listDataResponsavel,
    contaCliente,
    listServicosAdicionais,
    setListServicosAdicionais,
    isPlanoAdicionado,
  } = useAssinaturasContext();

  const formMethods = useForm<FormData>({
    resolver: isCriarNovaLoja
      ? yupResolverCadastroLoja
      : yupResolverCadastroCompleto,
    defaultValues: {
      ...formDefaultValues,
      diaVencimento: Number(formDefaultValues.diaVencimento),
      dataExpiracao: formDefaultValues.dataExpiracao
    },
  });

  const { reset, handleSubmit: onSubmit } = formMethods;

  const navigate = useNavigate();

  const getResponseCadastroCliente = useCallback(
    async (data: FormData) => {
      const propsAssinaturaCadastrar = dadosApiAssinatura({
        data,
        servicos: listServicosAdicionais,
        naoExibirResponsavel: false,
      });

      const response = await api.post<void, ResponseApi<FormData>>(
        ConstantEnderecoWebservice.CADASTRAR_ASSINATURA,
        {
          ...propsAssinaturaCadastrar,
        }
      );

      return response;
    },
    [dadosApiAssinatura, listServicosAdicionais]
  );

  const getResponseCadastroNovaLoja = useCallback(
    async (data: FormData) => {
      const propsAssinaturaCadastrar = dadosApiAssinatura({
        data,
        servicos: listServicosAdicionais,
        naoExibirResponsavel: true,
      });

      const response = await api.post<void, ResponseApi<FormData>>(
        `${ConstantEnderecoWebservice.CADASTRAR_ASSINATURA_LOJA}?contaClienteId=${contaCliente}&idAssinaturaBasePrecos=${data.idAssinaturaBasePrecos}`,
        {
          ...propsAssinaturaCadastrar,
        }
      );

      return response;
    },
    [dadosApiAssinatura, listServicosAdicionais]
  );

  const handleSubmit = onSubmit(async (data: FormData) => {
    setIsLoading(true);
    try {
      // Ensure dataExpiracao is a valid Date before sending
      if (!(data.dataExpiracao instanceof Date) || isNaN(data.dataExpiracao.getTime())) {
        data.dataExpiracao = getDefaultExpirationDate(); // Default to current date if invalid
      }
      
      const propsAssinaturaCadastrar = dadosApiAssinatura({
        data,
        servicos: listServicosAdicionais,
        naoExibirResponsavel: false,
      });

      const response = await api.post<void, ResponseApi<FormData>>(
        ConstantEnderecoWebservice.CADASTRAR_ASSINATURA,
        {
          ...propsAssinaturaCadastrar,
        }
      );

      if (response.sucesso) {
        toast.success('Cadastro realizado com sucesso.');
        navigate(ConstanteRotas.ASSINATURAS);
      }
    } catch (error) {
      console.error('Erro ao cadastrar assinatura:', error);
      toast.error('Erro ao cadastrar assinatura. Verifique os dados e tente novamente.');
    } finally {
      setIsLoading(false);
    }
  });

  const handleSubmitReset = onSubmit(async (data) => {
    setIsLoading(true);
    const response = await getResponseCadastroCliente(data);

    if (response.sucesso) {
      reset({
        ...formDefaultValues,
        diaVencimento: Number(formDefaultValues.diaVencimento)
      });
      setListServicosAdicionais([]);
      toast.success('Cadastro realizado com sucesso.');
    }
    setIsLoading(false);
  });

  useEffect(() => {
    if (isCriarNovaLoja) {
      reset({
        ...listDataResponsavel,
        dominio: listDataResponsavel.dominio?.split('.')[0],
        telefoneResponsavel: listDataResponsavel.celular,
      });
    }
  }, [isCriarNovaLoja, listDataResponsavel, reset]);

  return (
    <LayoutFormPage
      onSubmit={handleSubmit}
      onResetSubmit={!isCriarNovaLoja ? handleSubmitReset : undefined}
      isLoading={isLoading}
      isDisabledReset={!isPlanoAdicionado}
      isDisabledSubmit={!isPlanoAdicionado}
    >
      <FormProvider {...formMethods}>
        <FormAssinaturas isCadastrar />
      </FormProvider>
    </LayoutFormPage>
  );
};
